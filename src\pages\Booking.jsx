import React from 'react';
import { Helmet } from 'react-helmet-async';
import BookingForm from '../components/BookingForm';

const Booking = () => {
  const quickInfo = [
    {
      icon: '📞',
      title: 'Call Us',
      description: '+91 98765 43210',
      action: 'tel:+919876543210'
    },
    {
      icon: '📧',
      title: 'Email Us',
      description: '<EMAIL>',
      action: 'mailto:<EMAIL>'
    },
    {
      icon: '💬',
      title: 'WhatsApp',
      description: 'Quick Response',
      action: 'https://wa.me/919876543210'
    }
  ];

  const bookingSteps = [
    {
      step: '1',
      title: 'Choose Your Service',
      description: 'Select from our range of boat charter services'
    },
    {
      step: '2',
      title: 'Fill the Form',
      description: 'Provide your details and preferences'
    },
    {
      step: '3',
      title: 'Get Confirmation',
      description: 'We\'ll contact you within 2 hours to confirm'
    },
    {
      step: '4',
      title: 'Make Payment',
      description: 'Secure your booking with advance payment'
    }
  ];

  const policies = [
    {
      title: 'Booking Policy',
      points: [
        '50% advance payment required to confirm booking',
        'Balance payment can be made on the day of service',
        'Booking confirmation will be sent via email/SMS',
        'Valid ID proof required for all passengers'
      ]
    },
    {
      title: 'Cancellation Policy',
      points: [
        'Free cancellation up to 24 hours before departure',
        'Cancellation within 24 hours: 50% refund',
        'No refund for no-shows or same-day cancellations',
        'Weather-related cancellations: Full refund or reschedule'
      ]
    },
    {
      title: 'Safety Guidelines',
      points: [
        'Life jackets mandatory for all passengers',
        'Children under 12 must be accompanied by adults',
        'No alcohol consumption during tours',
        'Follow crew instructions at all times'
      ]
    }
  ];

  return (
    <>
      <Helmet>
        <title>Book Your Charter - Andaman Charters | Online Booking</title>
        <meta name="description" content="Book your boat charter in Andaman Islands online. Easy booking process for private tours, fishing charters, and island hopping adventures." />
        <meta name="keywords" content="book Andaman boat charter, online booking, boat tour reservation, fishing charter booking" />
      </Helmet>

      <div className="pt-16">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-r from-ocean-600 to-ocean-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Book Your Charter
            </h1>
            <p className="text-xl text-ocean-100 max-w-3xl mx-auto">
              Reserve your spot for an unforgettable maritime adventure in the Andaman Islands
            </p>
          </div>
        </section>

        {/* Quick Contact Options */}
        <section className="py-8 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Need Immediate Assistance?</h2>
              <p className="text-gray-600">Contact us directly for instant booking support</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {quickInfo.map((info, index) => (
                <a
                  key={index}
                  href={info.action}
                  className="bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow duration-200"
                >
                  <div className="text-3xl mb-3">{info.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">{info.title}</h3>
                  <p className="text-ocean-600">{info.description}</p>
                </a>
              ))}
            </div>
          </div>
        </section>

        {/* Booking Steps */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">How to Book</h2>
              <p className="text-lg text-gray-600">Simple steps to secure your charter</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {bookingSteps.map((step, index) => (
                <div key={index} className="text-center">
                  <div className="bg-ocean-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {step.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h3>
                  <p className="text-gray-600">{step.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Main Booking Section */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              {/* Booking Form */}
              <div className="lg:col-span-2">
                <BookingForm />
              </div>

              {/* Sidebar Information */}
              <div className="space-y-6">
                {/* Popular Services */}
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Popular Services</h3>
                  <div className="space-y-4">
                    <div className="border-l-4 border-ocean-500 pl-4">
                      <h4 className="font-semibold text-gray-900">Half Day Island Tour</h4>
                      <p className="text-gray-600 text-sm">4 hours • From ₹8,000</p>
                    </div>
                    <div className="border-l-4 border-ocean-500 pl-4">
                      <h4 className="font-semibold text-gray-900">Game Fishing Charter</h4>
                      <p className="text-gray-600 text-sm">6 hours • From ₹15,000</p>
                    </div>
                    <div className="border-l-4 border-ocean-500 pl-4">
                      <h4 className="font-semibold text-gray-900">Sunset Cruise</h4>
                      <p className="text-gray-600 text-sm">2 hours • From ₹5,000</p>
                    </div>
                  </div>
                </div>

                {/* What to Bring */}
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">What to Bring</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Valid photo ID
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Sunscreen and hat
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Comfortable clothing
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Camera/phone
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Towel (for water activities)
                    </li>
                  </ul>
                </div>

                {/* Weather Notice */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="flex items-start">
                    <svg className="w-6 h-6 text-yellow-600 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <div>
                      <h4 className="text-lg font-semibold text-yellow-800 mb-2">Weather Dependent</h4>
                      <p className="text-yellow-700 text-sm">
                        All boat services are subject to weather conditions. In case of rough seas or storms, 
                        we may need to reschedule or cancel for safety reasons. Full refund or rescheduling 
                        will be offered in such cases.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Policies Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Booking Policies</h2>
              <p className="text-lg text-gray-600">Please read our policies before booking</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {policies.map((policy, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">{policy.title}</h3>
                  <ul className="space-y-2">
                    {policy.points.map((point, idx) => (
                      <li key={idx} className="text-gray-600 text-sm">
                        • {point}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Booking;
