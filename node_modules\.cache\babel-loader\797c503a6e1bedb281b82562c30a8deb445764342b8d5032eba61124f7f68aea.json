{"ast": null, "code": "var _jsxFileName = \"E:\\\\boat\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { HelmetProvider } from 'react-helmet-async';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\n\n// Pages\nimport Home from './pages/Home';\nimport AboutUs from './pages/AboutUs';\nimport Services from './pages/Services';\nimport Booking from './pages/Booking';\nimport GalleryPage from './pages/GalleryPage';\nimport ContactUs from './pages/ContactUs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(HelmetProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App min-h-screen flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-grow\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/about\",\n              element: /*#__PURE__*/_jsxDEV(AboutUs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/services\",\n              element: /*#__PURE__*/_jsxDEV(Services, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/booking\",\n              element: /*#__PURE__*/_jsxDEV(Booking, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/gallery\",\n              element: /*#__PURE__*/_jsxDEV(GalleryPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/contact\",\n              element: /*#__PURE__*/_jsxDEV(ContactUs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Footer", "Home", "AboutUs", "Services", "Booking", "GalleryPage", "ContactUs", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["E:/boat/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { HelmetProvider } from 'react-helmet-async';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\n\n// Pages\nimport Home from './pages/Home';\nimport AboutUs from './pages/AboutUs';\nimport Services from './pages/Services';\nimport Booking from './pages/Booking';\nimport GalleryPage from './pages/GalleryPage';\nimport ContactUs from './pages/ContactUs';\n\nfunction App() {\n  return (\n    <HelmetProvider>\n      <Router>\n        <div className=\"App min-h-screen flex flex-col\">\n          <Navbar />\n          <main className=\"flex-grow\">\n            <Routes>\n              <Route path=\"/\" element={<Home />} />\n              <Route path=\"/about\" element={<AboutUs />} />\n              <Route path=\"/services\" element={<Services />} />\n              <Route path=\"/booking\" element={<Booking />} />\n              <Route path=\"/gallery\" element={<GalleryPage />} />\n              <Route path=\"/contact\" element={<ContactUs />} />\n            </Routes>\n          </main>\n          <Footer />\n        </div>\n      </Router>\n    </HelmetProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,cAAc,QAAQ,oBAAoB;;AAEnD;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;;AAExC;AACA,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,cAAc;IAAAY,QAAA,eACbF,OAAA,CAACb,MAAM;MAAAe,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,gCAAgC;QAAAD,QAAA,gBAC7CF,OAAA,CAACT,MAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVP,OAAA;UAAMG,SAAS,EAAC,WAAW;UAAAD,QAAA,eACzBF,OAAA,CAACZ,MAAM;YAAAc,QAAA,gBACLF,OAAA,CAACX,KAAK;cAACmB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAET,OAAA,CAACP,IAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrCP,OAAA,CAACX,KAAK;cAACmB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAET,OAAA,CAACN,OAAO;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CP,OAAA,CAACX,KAAK;cAACmB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAET,OAAA,CAACL,QAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDP,OAAA,CAACX,KAAK;cAACmB,IAAI,EAAC,UAAU;cAACC,OAAO,eAAET,OAAA,CAACJ,OAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CP,OAAA,CAACX,KAAK;cAACmB,IAAI,EAAC,UAAU;cAACC,OAAO,eAAET,OAAA,CAACH,WAAW;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDP,OAAA,CAACX,KAAK;cAACmB,IAAI,EAAC,UAAU;cAACC,OAAO,eAAET,OAAA,CAACF,SAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACPP,OAAA,CAACR,MAAM;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB;AAACG,EAAA,GArBQT,GAAG;AAuBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}