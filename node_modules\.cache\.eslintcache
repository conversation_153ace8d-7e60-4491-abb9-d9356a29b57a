[{"E:\\boat\\src\\index.js": "1", "E:\\boat\\src\\reportWebVitals.js": "2", "E:\\boat\\src\\App.js": "3", "E:\\boat\\src\\pages\\AboutUs.jsx": "4", "E:\\boat\\src\\components\\Navbar.jsx": "5", "E:\\boat\\src\\components\\Footer.jsx": "6", "E:\\boat\\src\\pages\\Services.jsx": "7", "E:\\boat\\src\\pages\\Home.jsx": "8", "E:\\boat\\src\\pages\\ContactUs.jsx": "9", "E:\\boat\\src\\pages\\Booking.jsx": "10", "E:\\boat\\src\\pages\\GalleryPage.jsx": "11", "E:\\boat\\src\\components\\Gallery.jsx": "12", "E:\\boat\\src\\components\\EnquiryForm.jsx": "13", "E:\\boat\\src\\components\\BookingForm.jsx": "14"}, {"size": 535, "mtime": 1749747582452, "results": "15", "hashOfConfig": "16"}, {"size": 362, "mtime": 1749747582501, "results": "17", "hashOfConfig": "16"}, {"size": 1219, "mtime": 1749748427848, "results": "18", "hashOfConfig": "16"}, {"size": 14114, "mtime": 1749748183716, "results": "19", "hashOfConfig": "16"}, {"size": 3580, "mtime": 1749747960339, "results": "20", "hashOfConfig": "16"}, {"size": 6666, "mtime": 1749747993680, "results": "21", "hashOfConfig": "16"}, {"size": 15438, "mtime": 1749748244823, "results": "22", "hashOfConfig": "16"}, {"size": 11186, "mtime": 1749748128771, "results": "23", "hashOfConfig": "16"}, {"size": 13656, "mtime": 1749748414533, "results": "24", "hashOfConfig": "16"}, {"size": 11630, "mtime": 1749748292486, "results": "25", "hashOfConfig": "16"}, {"size": 13897, "mtime": 1749748354654, "results": "26", "hashOfConfig": "16"}, {"size": 6182, "mtime": 1749748082582, "results": "27", "hashOfConfig": "16"}, {"size": 7202, "mtime": 1749748052398, "results": "28", "hashOfConfig": "16"}, {"size": 7953, "mtime": 1749748023156, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "psafgt", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\boat\\src\\index.js", [], [], "E:\\boat\\src\\reportWebVitals.js", [], [], "E:\\boat\\src\\App.js", [], [], "E:\\boat\\src\\pages\\AboutUs.jsx", [], [], "E:\\boat\\src\\components\\Navbar.jsx", [], [], "E:\\boat\\src\\components\\Footer.jsx", ["72", "73", "74"], [], "E:\\boat\\src\\pages\\Services.jsx", [], [], "E:\\boat\\src\\pages\\Home.jsx", [], [], "E:\\boat\\src\\pages\\ContactUs.jsx", ["75", "76", "77"], [], "E:\\boat\\src\\pages\\Booking.jsx", [], [], "E:\\boat\\src\\pages\\GalleryPage.jsx", ["78", "79"], [], "E:\\boat\\src\\components\\Gallery.jsx", [], [], "E:\\boat\\src\\components\\EnquiryForm.jsx", [], [], "E:\\boat\\src\\components\\BookingForm.jsx", [], [], {"ruleId": "80", "severity": 1, "message": "81", "line": 21, "column": 15, "nodeType": "82", "endLine": 21, "endColumn": 92}, {"ruleId": "80", "severity": 1, "message": "81", "line": 27, "column": 15, "nodeType": "82", "endLine": 27, "endColumn": 92}, {"ruleId": "80", "severity": 1, "message": "81", "line": 33, "column": 15, "nodeType": "82", "endLine": 33, "endColumn": 92}, {"ruleId": "80", "severity": 1, "message": "81", "line": 237, "column": 15, "nodeType": "82", "endLine": 237, "endColumn": 115}, {"ruleId": "80", "severity": 1, "message": "81", "line": 244, "column": 15, "nodeType": "82", "endLine": 244, "endColumn": 115}, {"ruleId": "80", "severity": 1, "message": "81", "line": 251, "column": 15, "nodeType": "82", "endLine": 251, "endColumn": 117}, {"ruleId": "80", "severity": 1, "message": "81", "line": 267, "column": 17, "nodeType": "82", "endLine": 267, "endColumn": 95}, {"ruleId": "80", "severity": 1, "message": "81", "line": 273, "column": 17, "nodeType": "82", "endLine": 273, "endColumn": 95}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]