import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

const Services = () => {
  const services = [
    {
      id: 'private-tours',
      title: 'Private Boat Tours',
      subtitle: 'Exclusive island experiences',
      description: 'Explore the pristine waters and hidden gems of the Andaman Islands with our private boat tours. Perfect for families, couples, or small groups seeking a personalized experience.',
      features: [
        'Customizable itineraries',
        'Professional guide included',
        'Snorkeling equipment provided',
        'Refreshments and lunch',
        'Photography assistance',
        'Flexible timing'
      ],
      pricing: 'Starting from ₹8,000 for half-day tours',
      duration: '4-8 hours',
      capacity: 'Up to 12 passengers',
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      highlights: [
        'Visit Ross Island and North Bay',
        'Explore Elephant Beach',
        'Discover hidden lagoons',
        'Pristine coral reefs'
      ]
    },
    {
      id: 'game-fishing',
      title: 'Game Fishing Charters',
      subtitle: 'Deep sea fishing adventures',
      description: 'Experience the thrill of deep-sea fishing in the rich waters surrounding the Andaman Islands. Our experienced fishing guides will take you to the best spots for an unforgettable angling experience.',
      features: [
        'Professional fishing equipment',
        'Expert fishing guides',
        'Fish finder technology',
        'Live bait and tackle',
        'Fish cleaning service',
        'Catch photography'
      ],
      pricing: 'Starting from ₹15,000 for full-day charters',
      duration: '6-8 hours',
      capacity: 'Up to 6 anglers',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      highlights: [
        'Target species: Tuna, Marlin, Sailfish',
        'Deep sea trolling',
        'Bottom fishing',
        'Catch and release options'
      ]
    },
    {
      id: 'sunset-cruises',
      title: 'Sunset Cruises',
      subtitle: 'Romantic evening experiences',
      description: 'End your day with a magical sunset cruise around the Andaman waters. Perfect for couples, special occasions, or anyone wanting to witness the spectacular Andaman sunset.',
      features: [
        'Romantic ambiance',
        'Complimentary beverages',
        'Light snacks included',
        'Music system available',
        'Photography service',
        'Comfortable seating'
      ],
      pricing: 'Starting from ₹5,000 per couple',
      duration: '2-3 hours',
      capacity: 'Up to 10 passengers',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      highlights: [
        'Spectacular sunset views',
        'Calm evening waters',
        'Perfect for proposals',
        'Anniversary celebrations'
      ]
    },
    {
      id: 'island-hopping',
      title: 'Island Hopping',
      subtitle: 'Multi-island adventures',
      description: 'Discover multiple islands in a single day with our comprehensive island hopping tours. Visit the most beautiful and accessible islands around Port Blair and beyond.',
      features: [
        'Multiple island visits',
        'Beach time at each stop',
        'Snorkeling opportunities',
        'Local guide commentary',
        'Packed lunch included',
        'Transportation between islands'
      ],
      pricing: 'Starting from ₹12,000 for full-day tours',
      duration: '8-10 hours',
      capacity: 'Up to 15 passengers',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      highlights: [
        'Ross Island historical tour',
        'Viper Island exploration',
        'North Bay coral viewing',
        'Beach activities'
      ]
    }
  ];

  const additionalServices = [
    {
      icon: '🎉',
      title: 'Special Events',
      description: 'Birthday parties, anniversaries, corporate events, and celebrations'
    },
    {
      icon: '📸',
      title: 'Photography Tours',
      description: 'Specialized tours for photographers with optimal lighting and locations'
    },
    {
      icon: '🤿',
      title: 'Diving Expeditions',
      description: 'Scuba diving trips to the best dive sites around the islands'
    },
    {
      icon: '🏖️',
      title: 'Beach Transfers',
      description: 'Transportation to remote beaches and secluded locations'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Our Services - Andaman Charters | Boat Tours & Fishing Charters</title>
        <meta name="description" content="Explore our premium boat charter services in Andaman - private tours, game fishing, sunset cruises, and island hopping adventures." />
        <meta name="keywords" content="Andaman boat tours, game fishing charters, sunset cruises, island hopping, private boat rental" />
      </Helmet>

      <div className="pt-16">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-r from-ocean-600 to-ocean-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our Services
            </h1>
            <p className="text-xl text-ocean-100 max-w-3xl mx-auto">
              Discover the beauty of Andaman Islands with our comprehensive range of boat charter services
            </p>
          </div>
        </section>

        {/* Main Services */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="space-y-16">
              {services.map((service, index) => (
                <div key={service.id} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                  <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                    <div className="bg-ocean-50 inline-block px-3 py-1 rounded-full text-ocean-700 text-sm font-medium mb-4">
                      {service.subtitle}
                    </div>
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                      {service.title}
                    </h2>
                    <p className="text-lg text-gray-600 mb-6">
                      {service.description}
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-white p-4 rounded-lg border">
                        <h4 className="font-semibold text-gray-900 mb-1">Duration</h4>
                        <p className="text-gray-600">{service.duration}</p>
                      </div>
                      <div className="bg-white p-4 rounded-lg border">
                        <h4 className="font-semibold text-gray-900 mb-1">Capacity</h4>
                        <p className="text-gray-600">{service.capacity}</p>
                      </div>
                      <div className="bg-white p-4 rounded-lg border">
                        <h4 className="font-semibold text-gray-900 mb-1">Pricing</h4>
                        <p className="text-ocean-600 font-semibold">{service.pricing}</p>
                      </div>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3">What's Included:</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {service.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center">
                            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            <span className="text-gray-600">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Highlights:</h4>
                      <ul className="space-y-1">
                        {service.highlights.map((highlight, idx) => (
                          <li key={idx} className="text-gray-600">• {highlight}</li>
                        ))}
                      </ul>
                    </div>

                    <Link
                      to="/booking"
                      className="inline-block bg-ocean-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-ocean-700 transition-colors duration-200"
                    >
                      Book This Service
                    </Link>
                  </div>
                  
                  <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                    <img
                      src={service.image}
                      alt={service.title}
                      className="rounded-lg shadow-lg w-full h-96 object-cover"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Additional Services */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Additional Services
              </h2>
              <p className="text-xl text-gray-600">
                We also offer specialized services for unique experiences
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {additionalServices.map((service, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Information */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-ocean-50 rounded-lg p-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Pricing Information
                </h2>
                <p className="text-lg text-gray-600">
                  All prices are starting rates and may vary based on season, group size, and specific requirements
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">What's Included in All Tours:</h3>
                  <ul className="space-y-2">
                    <li className="flex items-center text-gray-600">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Professional captain and crew
                    </li>
                    <li className="flex items-center text-gray-600">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Safety equipment and life jackets
                    </li>
                    <li className="flex items-center text-gray-600">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Fuel and port charges
                    </li>
                    <li className="flex items-center text-gray-600">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Basic refreshments
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Booking Information:</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• 50% advance payment required for booking</li>
                    <li>• Free cancellation up to 24 hours before departure</li>
                    <li>• Group discounts available for 8+ passengers</li>
                    <li>• Custom packages can be arranged</li>
                    <li>• Seasonal pricing may apply during peak months</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-ocean-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to Book Your Adventure?
            </h2>
            <p className="text-xl text-ocean-100 mb-8 max-w-2xl mx-auto">
              Contact us today to customize your perfect Andaman experience
            </p>
            <div className="space-x-4">
              <Link
                to="/booking"
                className="inline-block bg-white text-ocean-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
              >
                Book Now
              </Link>
              <Link
                to="/contact"
                className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-ocean-600 transition-colors duration-200"
              >
                Get Quote
              </Link>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Services;
