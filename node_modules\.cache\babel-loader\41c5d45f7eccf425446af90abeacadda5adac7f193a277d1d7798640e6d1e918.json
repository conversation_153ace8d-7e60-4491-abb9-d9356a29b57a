{"ast": null, "code": "var _jsxFileName = \"E:\\\\boat\\\\src\\\\pages\\\\Home.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Helmet } from 'react-helmet-async';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  const features = [{\n    icon: '🚤',\n    title: 'Luxury Fleet',\n    description: 'Modern, well-maintained boats with all safety equipment and amenities for your comfort.'\n  }, {\n    icon: '🎣',\n    title: 'Expert Guides',\n    description: 'Experienced local captains and fishing guides who know the best spots and conditions.'\n  }, {\n    icon: '🏝️',\n    title: 'Island Hopping',\n    description: 'Explore pristine beaches and hidden coves across the beautiful Andaman archipelago.'\n  }, {\n    icon: '🌅',\n    title: 'Sunset Cruises',\n    description: 'Romantic sunset experiences with breathtaking views of the Indian Ocean.'\n  }];\n  const testimonials = [{\n    name: '<PERSON>',\n    location: 'Mumbai, India',\n    text: 'Absolutely incredible experience! The boat was luxurious and the crew was professional. The fishing was amazing and we caught some beautiful fish.',\n    rating: 5\n  }, {\n    name: '<PERSON>',\n    location: 'Singapore',\n    text: 'Perfect day out with family. The island hopping tour was well organized and the kids loved snorkeling in the crystal clear waters.',\n    rating: 5\n  }, {\n    name: 'Priya <PERSON>',\n    location: 'Delhi, India',\n    text: 'The sunset cruise was magical! Great service, beautiful boat, and unforgettable memories. Highly recommended for couples.',\n    rating: 5\n  }];\n  const packages = [{\n    title: 'Half Day Island Tour',\n    duration: '4 hours',\n    price: 'From ₹8,000',\n    features: ['Visit 2-3 islands', 'Snorkeling gear included', 'Refreshments', 'Professional guide'],\n    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'\n  }, {\n    title: 'Game Fishing Charter',\n    duration: '6 hours',\n    price: 'From ₹15,000',\n    features: ['Deep sea fishing', 'All equipment provided', 'Expert fishing guide', 'Lunch included'],\n    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'\n  }, {\n    title: 'Full Day Adventure',\n    duration: '8 hours',\n    price: 'From ₹20,000',\n    features: ['Multiple islands', 'Water sports', 'Beach BBQ lunch', 'Photography'],\n    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Andaman Charters - Premium Boat Charter Services in Andaman Islands\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Experience the pristine waters of Andaman with our premium boat charter services. Private tours, game fishing, and island hopping adventures.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"keywords\",\n        content: \"Andaman boat charter, private boat tours, game fishing Andaman, island hopping, yacht rental Andaman\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:title\",\n        content: \"Andaman Charters - Premium Boat Charter Services\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:description\",\n        content: \"Experience the pristine waters of Andaman with our premium boat charter services.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:type\",\n        content: \"website\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative h-screen flex items-center justify-center overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-cover bg-center bg-no-repeat\",\n        style: {\n          backgroundImage: 'url(https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-black bg-opacity-40\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 text-center text-white px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-6xl font-bold mb-6\",\n          children: [\"Discover Andaman's\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"block text-ocean-400\",\n            children: \"Hidden Paradise\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl md:text-2xl mb-8 max-w-3xl mx-auto\",\n          children: \"Experience luxury boat charters, game fishing, and island hopping in the pristine waters of the Andaman Islands\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/booking\",\n            className: \"inline-block bg-ocean-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-ocean-700 transition-colors duration-200\",\n            children: \"Book Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/services\",\n            className: \"inline-block border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-900 transition-colors duration-200\",\n            children: \"Our Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Why Choose Andaman Charters?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"We provide exceptional maritime experiences with safety, comfort, and adventure at the forefront\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Popular Packages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600\",\n            children: \"Choose from our carefully crafted experiences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: packages.map((pkg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: pkg.image,\n              alt: pkg.title,\n              className: \"w-full h-48 object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: pkg.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: pkg.duration\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl font-bold text-ocean-600\",\n                  children: pkg.price\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2 mb-6\",\n                children: pkg.features.map((feature, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 text-green-500 mr-2\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 25\n                  }, this), feature]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/booking\",\n                className: \"w-full bg-ocean-600 text-white py-2 px-4 rounded-md hover:bg-ocean-700 transition-colors duration-200 text-center block\",\n                children: \"Book This Package\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"What Our Guests Say\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600\",\n            children: \"Real experiences from our satisfied customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: testimonials.map((testimonial, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [...Array(testimonial.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-yellow-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 23\n                }, this)\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4\",\n              children: [\"\\\"\", testimonial.text, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold text-gray-900\",\n                children: testimonial.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500\",\n                children: testimonial.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-ocean-600\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n          children: \"Ready for Your Adventure?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-ocean-100 mb-8 max-w-2xl mx-auto\",\n          children: \"Book your charter today and create memories that will last a lifetime in the beautiful Andaman Islands\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/booking\",\n            className: \"inline-block bg-white text-ocean-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-200\",\n            children: \"Book Your Charter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"inline-block border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-ocean-600 transition-colors duration-200\",\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "Link", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Home", "features", "icon", "title", "description", "testimonials", "name", "location", "text", "rating", "packages", "duration", "price", "image", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "property", "className", "style", "backgroundImage", "to", "map", "feature", "index", "pkg", "src", "alt", "idx", "fill", "viewBox", "fillRule", "d", "clipRule", "testimonial", "Array", "_", "i", "_c", "$RefreshReg$"], "sources": ["E:/boat/src/pages/Home.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Helmet } from 'react-helmet-async';\n\nconst Home = () => {\n  const features = [\n    {\n      icon: '🚤',\n      title: 'Luxury Fleet',\n      description: 'Modern, well-maintained boats with all safety equipment and amenities for your comfort.'\n    },\n    {\n      icon: '🎣',\n      title: 'Expert Guides',\n      description: 'Experienced local captains and fishing guides who know the best spots and conditions.'\n    },\n    {\n      icon: '🏝️',\n      title: 'Island Hopping',\n      description: 'Explore pristine beaches and hidden coves across the beautiful Andaman archipelago.'\n    },\n    {\n      icon: '🌅',\n      title: 'Sunset Cruises',\n      description: 'Romantic sunset experiences with breathtaking views of the Indian Ocean.'\n    }\n  ];\n\n  const testimonials = [\n    {\n      name: '<PERSON>',\n      location: 'Mumbai, India',\n      text: 'Absolutely incredible experience! The boat was luxurious and the crew was professional. The fishing was amazing and we caught some beautiful fish.',\n      rating: 5\n    },\n    {\n      name: '<PERSON>',\n      location: 'Singapore',\n      text: 'Perfect day out with family. The island hopping tour was well organized and the kids loved snorkeling in the crystal clear waters.',\n      rating: 5\n    },\n    {\n      name: '<PERSON><PERSON>',\n      location: 'Delhi, India',\n      text: 'The sunset cruise was magical! Great service, beautiful boat, and unforgettable memories. Highly recommended for couples.',\n      rating: 5\n    }\n  ];\n\n  const packages = [\n    {\n      title: 'Half Day Island Tour',\n      duration: '4 hours',\n      price: 'From ₹8,000',\n      features: ['Visit 2-3 islands', 'Snorkeling gear included', 'Refreshments', 'Professional guide'],\n      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'\n    },\n    {\n      title: 'Game Fishing Charter',\n      duration: '6 hours',\n      price: 'From ₹15,000',\n      features: ['Deep sea fishing', 'All equipment provided', 'Expert fishing guide', 'Lunch included'],\n      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'\n    },\n    {\n      title: 'Full Day Adventure',\n      duration: '8 hours',\n      price: 'From ₹20,000',\n      features: ['Multiple islands', 'Water sports', 'Beach BBQ lunch', 'Photography'],\n      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'\n    }\n  ];\n\n  return (\n    <>\n      <Helmet>\n        <title>Andaman Charters - Premium Boat Charter Services in Andaman Islands</title>\n        <meta name=\"description\" content=\"Experience the pristine waters of Andaman with our premium boat charter services. Private tours, game fishing, and island hopping adventures.\" />\n        <meta name=\"keywords\" content=\"Andaman boat charter, private boat tours, game fishing Andaman, island hopping, yacht rental Andaman\" />\n        <meta property=\"og:title\" content=\"Andaman Charters - Premium Boat Charter Services\" />\n        <meta property=\"og:description\" content=\"Experience the pristine waters of Andaman with our premium boat charter services.\" />\n        <meta property=\"og:type\" content=\"website\" />\n      </Helmet>\n\n      {/* Hero Section */}\n      <section className=\"relative h-screen flex items-center justify-center overflow-hidden\">\n        <div \n          className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n          style={{\n            backgroundImage: 'url(https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80)'\n          }}\n        >\n          <div className=\"absolute inset-0 bg-black bg-opacity-40\"></div>\n        </div>\n        \n        <div className=\"relative z-10 text-center text-white px-4 sm:px-6 lg:px-8\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            Discover Andaman's\n            <span className=\"block text-ocean-400\">Hidden Paradise</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 max-w-3xl mx-auto\">\n            Experience luxury boat charters, game fishing, and island hopping in the pristine waters of the Andaman Islands\n          </p>\n          <div className=\"space-x-4\">\n            <Link\n              to=\"/booking\"\n              className=\"inline-block bg-ocean-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-ocean-700 transition-colors duration-200\"\n            >\n              Book Now\n            </Link>\n            <Link\n              to=\"/services\"\n              className=\"inline-block border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-900 transition-colors duration-200\"\n            >\n              Our Services\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Why Choose Andaman Charters?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              We provide exceptional maritime experiences with safety, comfort, and adventure at the forefront\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {features.map((feature, index) => (\n              <div key={index} className=\"text-center\">\n                <div className=\"text-4xl mb-4\">{feature.icon}</div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{feature.title}</h3>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Packages Section */}\n      <section className=\"py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Popular Packages\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              Choose from our carefully crafted experiences\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {packages.map((pkg, index) => (\n              <div key={index} className=\"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\">\n                <img\n                  src={pkg.image}\n                  alt={pkg.title}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{pkg.title}</h3>\n                  <div className=\"flex justify-between items-center mb-4\">\n                    <span className=\"text-gray-600\">{pkg.duration}</span>\n                    <span className=\"text-2xl font-bold text-ocean-600\">{pkg.price}</span>\n                  </div>\n                  <ul className=\"space-y-2 mb-6\">\n                    {pkg.features.map((feature, idx) => (\n                      <li key={idx} className=\"flex items-center text-gray-600\">\n                        <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                  <Link\n                    to=\"/booking\"\n                    className=\"w-full bg-ocean-600 text-white py-2 px-4 rounded-md hover:bg-ocean-700 transition-colors duration-200 text-center block\"\n                  >\n                    Book This Package\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              What Our Guests Say\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              Real experiences from our satisfied customers\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {testimonials.map((testimonial, index) => (\n              <div key={index} className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center mb-4\">\n                  {[...Array(testimonial.rating)].map((_, i) => (\n                    <svg key={i} className=\"w-5 h-5 text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                    </svg>\n                  ))}\n                </div>\n                <p className=\"text-gray-600 mb-4\">\"{testimonial.text}\"</p>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">{testimonial.name}</p>\n                  <p className=\"text-gray-500\">{testimonial.location}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-16 bg-ocean-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n            Ready for Your Adventure?\n          </h2>\n          <p className=\"text-xl text-ocean-100 mb-8 max-w-2xl mx-auto\">\n            Book your charter today and create memories that will last a lifetime in the beautiful Andaman Islands\n          </p>\n          <div className=\"space-x-4\">\n            <Link\n              to=\"/booking\"\n              className=\"inline-block bg-white text-ocean-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-200\"\n            >\n              Book Your Charter\n            </Link>\n            <Link\n              to=\"/contact\"\n              className=\"inline-block border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-ocean-600 transition-colors duration-200\"\n            >\n              Contact Us\n            </Link>\n          </div>\n        </div>\n      </section>\n    </>\n  );\n};\n\nexport default Home;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,eAAe;IACrBC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,oJAAoJ;IAC1JC,MAAM,EAAE;EACV,CAAC,EACD;IACEH,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,oIAAoI;IAC1IC,MAAM,EAAE;EACV,CAAC,EACD;IACEH,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,2HAA2H;IACjIC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG,CACf;IACEP,KAAK,EAAE,sBAAsB;IAC7BQ,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,aAAa;IACpBX,QAAQ,EAAE,CAAC,mBAAmB,EAAE,0BAA0B,EAAE,cAAc,EAAE,oBAAoB,CAAC;IACjGY,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,sBAAsB;IAC7BQ,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,cAAc;IACrBX,QAAQ,EAAE,CAAC,kBAAkB,EAAE,wBAAwB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC;IAClGY,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,oBAAoB;IAC3BQ,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,cAAc;IACrBX,QAAQ,EAAE,CAAC,kBAAkB,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,CAAC;IAChFY,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEhB,OAAA,CAAAE,SAAA;IAAAe,QAAA,gBACEjB,OAAA,CAACF,MAAM;MAAAmB,QAAA,gBACLjB,OAAA;QAAAiB,QAAA,EAAO;MAAmE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClFrB,OAAA;QAAMS,IAAI,EAAC,aAAa;QAACa,OAAO,EAAC;MAA+I;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnLrB,OAAA;QAAMS,IAAI,EAAC,UAAU;QAACa,OAAO,EAAC;MAAsG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvIrB,OAAA;QAAMuB,QAAQ,EAAC,UAAU;QAACD,OAAO,EAAC;MAAkD;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvFrB,OAAA;QAAMuB,QAAQ,EAAC,gBAAgB;QAACD,OAAO,EAAC;MAAmF;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9HrB,OAAA;QAAMuB,QAAQ,EAAC,SAAS;QAACD,OAAO,EAAC;MAAS;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAGTrB,OAAA;MAASwB,SAAS,EAAC,oEAAoE;MAAAP,QAAA,gBACrFjB,OAAA;QACEwB,SAAS,EAAC,kDAAkD;QAC5DC,KAAK,EAAE;UACLC,eAAe,EAAE;QACnB,CAAE;QAAAT,QAAA,eAEFjB,OAAA;UAAKwB,SAAS,EAAC;QAAyC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAENrB,OAAA;QAAKwB,SAAS,EAAC,2DAA2D;QAAAP,QAAA,gBACxEjB,OAAA;UAAIwB,SAAS,EAAC,qCAAqC;UAAAP,QAAA,GAAC,oBAElD,eAAAjB,OAAA;YAAMwB,SAAS,EAAC,sBAAsB;YAAAP,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACLrB,OAAA;UAAGwB,SAAS,EAAC,4CAA4C;UAAAP,QAAA,EAAC;QAE1D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrB,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAP,QAAA,gBACxBjB,OAAA,CAACH,IAAI;YACH8B,EAAE,EAAC,UAAU;YACbH,SAAS,EAAC,mIAAmI;YAAAP,QAAA,EAC9I;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPrB,OAAA,CAACH,IAAI;YACH8B,EAAE,EAAC,WAAW;YACdH,SAAS,EAAC,4JAA4J;YAAAP,QAAA,EACvK;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASwB,SAAS,EAAC,kBAAkB;MAAAP,QAAA,eACnCjB,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAP,QAAA,gBACrDjB,OAAA;UAAKwB,SAAS,EAAC,mBAAmB;UAAAP,QAAA,gBAChCjB,OAAA;YAAIwB,SAAS,EAAC,mDAAmD;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrB,OAAA;YAAGwB,SAAS,EAAC,yCAAyC;YAAAP,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrB,OAAA;UAAKwB,SAAS,EAAC,sDAAsD;UAAAP,QAAA,EAClEb,QAAQ,CAACwB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3B9B,OAAA;YAAiBwB,SAAS,EAAC,aAAa;YAAAP,QAAA,gBACtCjB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAP,QAAA,EAAEY,OAAO,CAACxB;YAAI;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDrB,OAAA;cAAIwB,SAAS,EAAC,0CAA0C;cAAAP,QAAA,EAAEY,OAAO,CAACvB;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7ErB,OAAA;cAAGwB,SAAS,EAAC,eAAe;cAAAP,QAAA,EAAEY,OAAO,CAACtB;YAAW;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAH9CS,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASwB,SAAS,EAAC,OAAO;MAAAP,QAAA,eACxBjB,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAP,QAAA,gBACrDjB,OAAA;UAAKwB,SAAS,EAAC,mBAAmB;UAAAP,QAAA,gBAChCjB,OAAA;YAAIwB,SAAS,EAAC,mDAAmD;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrB,OAAA;YAAGwB,SAAS,EAAC,uBAAuB;YAAAP,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrB,OAAA;UAAKwB,SAAS,EAAC,uCAAuC;UAAAP,QAAA,EACnDJ,QAAQ,CAACe,GAAG,CAAC,CAACG,GAAG,EAAED,KAAK,kBACvB9B,OAAA;YAAiBwB,SAAS,EAAC,8FAA8F;YAAAP,QAAA,gBACvHjB,OAAA;cACEgC,GAAG,EAAED,GAAG,CAACf,KAAM;cACfiB,GAAG,EAAEF,GAAG,CAACzB,KAAM;cACfkB,SAAS,EAAC;YAA0B;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACFrB,OAAA;cAAKwB,SAAS,EAAC,KAAK;cAAAP,QAAA,gBAClBjB,OAAA;gBAAIwB,SAAS,EAAC,0CAA0C;gBAAAP,QAAA,EAAEc,GAAG,CAACzB;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzErB,OAAA;gBAAKwB,SAAS,EAAC,wCAAwC;gBAAAP,QAAA,gBACrDjB,OAAA;kBAAMwB,SAAS,EAAC,eAAe;kBAAAP,QAAA,EAAEc,GAAG,CAACjB;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrDrB,OAAA;kBAAMwB,SAAS,EAAC,mCAAmC;kBAAAP,QAAA,EAAEc,GAAG,CAAChB;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNrB,OAAA;gBAAIwB,SAAS,EAAC,gBAAgB;gBAAAP,QAAA,EAC3Bc,GAAG,CAAC3B,QAAQ,CAACwB,GAAG,CAAC,CAACC,OAAO,EAAEK,GAAG,kBAC7BlC,OAAA;kBAAcwB,SAAS,EAAC,iCAAiC;kBAAAP,QAAA,gBACvDjB,OAAA;oBAAKwB,SAAS,EAAC,6BAA6B;oBAACW,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAnB,QAAA,eAClFjB,OAAA;sBAAMqC,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,oHAAoH;sBAACC,QAAQ,EAAC;oBAAS;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClK,CAAC,EACLQ,OAAO;gBAAA,GAJDK,GAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKR,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLrB,OAAA,CAACH,IAAI;gBACH8B,EAAE,EAAC,UAAU;gBACbH,SAAS,EAAC,yHAAyH;gBAAAP,QAAA,EACpI;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GA5BES,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6BV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASwB,SAAS,EAAC,kBAAkB;MAAAP,QAAA,eACnCjB,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAP,QAAA,gBACrDjB,OAAA;UAAKwB,SAAS,EAAC,mBAAmB;UAAAP,QAAA,gBAChCjB,OAAA;YAAIwB,SAAS,EAAC,mDAAmD;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrB,OAAA;YAAGwB,SAAS,EAAC,uBAAuB;YAAAP,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrB,OAAA;UAAKwB,SAAS,EAAC,uCAAuC;UAAAP,QAAA,EACnDT,YAAY,CAACoB,GAAG,CAAC,CAACY,WAAW,EAAEV,KAAK,kBACnC9B,OAAA;YAAiBwB,SAAS,EAAC,mCAAmC;YAAAP,QAAA,gBAC5DjB,OAAA;cAAKwB,SAAS,EAAC,wBAAwB;cAAAP,QAAA,EACpC,CAAC,GAAGwB,KAAK,CAACD,WAAW,CAAC5B,MAAM,CAAC,CAAC,CAACgB,GAAG,CAAC,CAACc,CAAC,EAAEC,CAAC,kBACvC3C,OAAA;gBAAawB,SAAS,EAAC,yBAAyB;gBAACW,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAnB,QAAA,eACtFjB,OAAA;kBAAMsC,CAAC,EAAC;gBAA0V;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC,GAD7VsB,CAAC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrB,OAAA;cAAGwB,SAAS,EAAC,oBAAoB;cAAAP,QAAA,GAAC,IAAC,EAACuB,WAAW,CAAC7B,IAAI,EAAC,IAAC;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1DrB,OAAA;cAAAiB,QAAA,gBACEjB,OAAA;gBAAGwB,SAAS,EAAC,6BAA6B;gBAAAP,QAAA,EAAEuB,WAAW,CAAC/B;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjErB,OAAA;gBAAGwB,SAAS,EAAC,eAAe;gBAAAP,QAAA,EAAEuB,WAAW,CAAC9B;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA,GAZES,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASwB,SAAS,EAAC,oBAAoB;MAAAP,QAAA,eACrCjB,OAAA;QAAKwB,SAAS,EAAC,oDAAoD;QAAAP,QAAA,gBACjEjB,OAAA;UAAIwB,SAAS,EAAC,gDAAgD;UAAAP,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrB,OAAA;UAAGwB,SAAS,EAAC,+CAA+C;UAAAP,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrB,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAP,QAAA,gBACxBjB,OAAA,CAACH,IAAI;YACH8B,EAAE,EAAC,UAAU;YACbH,SAAS,EAAC,kIAAkI;YAAAP,QAAA,EAC7I;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPrB,OAAA,CAACH,IAAI;YACH8B,EAAE,EAAC,UAAU;YACbH,SAAS,EAAC,6JAA6J;YAAAP,QAAA,EACxK;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACV,CAAC;AAEP,CAAC;AAACuB,EAAA,GAzPIzC,IAAI;AA2PV,eAAeA,IAAI;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}