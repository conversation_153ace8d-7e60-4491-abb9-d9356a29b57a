{"ast": null, "code": "var _jsxFileName = \"E:\\\\boat\\\\src\\\\components\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const location = useLocation();\n  const navigation = [{\n    name: 'Home',\n    href: '/'\n  }, {\n    name: 'About Us',\n    href: '/about'\n  }, {\n    name: 'Services',\n    href: '/services'\n  }, {\n    name: 'Gallery',\n    href: '/gallery'\n  }, {\n    name: 'Book Now',\n    href: '/booking'\n  }, {\n    name: 'Contact',\n    href: '/contact'\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white shadow-lg fixed w-full z-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex-shrink-0 flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-ocean-600\",\n              children: \"\\uD83D\\uDEA4 Andaman Charters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-8\",\n          children: navigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.href,\n            className: `px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? 'text-ocean-600 bg-ocean-50' : 'text-gray-700 hover:text-ocean-600 hover:bg-ocean-50'}`,\n            children: item.name\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsOpen(!isOpen),\n            className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-ocean-600 hover:bg-ocean-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-ocean-500\",\n            \"aria-expanded\": \"false\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sr-only\",\n              children: \"Open main menu\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), !isOpen ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"block h-6 w-6\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"block h-6 w-6\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white shadow-lg\",\n        children: navigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.href,\n          className: `block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${isActive(item.href) ? 'text-ocean-600 bg-ocean-50' : 'text-gray-700 hover:text-ocean-600 hover:bg-ocean-50'}`,\n          onClick: () => setIsOpen(false),\n          children: item.name\n        }, item.name, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"ZZ0fXhV/KQDotcppCZkKk+L1II0=\", false, function () {\n  return [useLocation];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "isOpen", "setIsOpen", "location", "navigation", "name", "href", "isActive", "path", "pathname", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["E:/boat/src/components/Navbar.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst Navbar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const location = useLocation();\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'About Us', href: '/about' },\n    { name: 'Services', href: '/services' },\n    { name: 'Gallery', href: '/gallery' },\n    { name: 'Book Now', href: '/booking' },\n    { name: 'Contact', href: '/contact' },\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <nav className=\"bg-white shadow-lg fixed w-full z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link to=\"/\" className=\"flex-shrink-0 flex items-center\">\n              <span className=\"text-2xl font-bold text-ocean-600\">\n                🚤 Andaman Charters\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                to={item.href}\n                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-ocean-600 bg-ocean-50'\n                    : 'text-gray-700 hover:text-ocean-600 hover:bg-ocean-50'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-ocean-600 hover:bg-ocean-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-ocean-500\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isOpen ? (\n                <svg className=\"block h-6 w-6\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              ) : (\n                <svg className=\"block h-6 w-6\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white shadow-lg\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                to={item.href}\n                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-ocean-600 bg-ocean-50'\n                    : 'text-gray-700 hover:text-ocean-600 hover:bg-ocean-50'\n                }`}\n                onClick={() => setIsOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAMS,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,MAAMQ,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC3B;IAAED,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAS,CAAC,EACpC;IAAED,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACvC;IAAED,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,EACrC;IAAED,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACtC;IAAED,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,CACtC;EAED,MAAMC,QAAQ,GAAIC,IAAI,IAAKL,QAAQ,CAACM,QAAQ,KAAKD,IAAI;EAErD,oBACEV,OAAA;IAAKY,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBACnDb,OAAA;MAAKY,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDb,OAAA;QAAKY,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCb,OAAA;UAAKY,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCb,OAAA,CAACH,IAAI;YAACiB,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,iCAAiC;YAAAC,QAAA,eACtDb,OAAA;cAAMY,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEpD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNlB,OAAA;UAAKY,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDP,UAAU,CAACa,GAAG,CAAEC,IAAI,iBACnBpB,OAAA,CAACH,IAAI;YAEHiB,EAAE,EAAEM,IAAI,CAACZ,IAAK;YACdI,SAAS,EAAE,2EACTH,QAAQ,CAACW,IAAI,CAACZ,IAAI,CAAC,GACf,4BAA4B,GAC5B,sDAAsD,EACzD;YAAAK,QAAA,EAEFO,IAAI,CAACb;UAAI,GARLa,IAAI,CAACb,IAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlB,OAAA;UAAKY,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1Cb,OAAA;YACEqB,OAAO,EAAEA,CAAA,KAAMjB,SAAS,CAAC,CAACD,MAAM,CAAE;YAClCS,SAAS,EAAC,mLAAmL;YAC7L,iBAAc,OAAO;YAAAC,QAAA,gBAErBb,OAAA;cAAMY,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC9C,CAACf,MAAM,gBACNH,OAAA;cAAKY,SAAS,EAAC,eAAe;cAACU,KAAK,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAZ,QAAA,eACrHb,OAAA;gBAAM0B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,gBAENlB,OAAA;cAAKY,SAAS,EAAC,eAAe;cAACU,KAAK,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAZ,QAAA,eACrHb,OAAA;gBAAM0B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLf,MAAM,iBACLH,OAAA;MAAKY,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBb,OAAA;QAAKY,SAAS,EAAC,qDAAqD;QAAAC,QAAA,EACjEP,UAAU,CAACa,GAAG,CAAEC,IAAI,iBACnBpB,OAAA,CAACH,IAAI;UAEHiB,EAAE,EAAEM,IAAI,CAACZ,IAAK;UACdI,SAAS,EAAE,mFACTH,QAAQ,CAACW,IAAI,CAACZ,IAAI,CAAC,GACf,4BAA4B,GAC5B,sDAAsD,EACzD;UACHa,OAAO,EAAEA,CAAA,KAAMjB,SAAS,CAAC,KAAK,CAAE;UAAAS,QAAA,EAE/BO,IAAI,CAACb;QAAI,GATLa,IAAI,CAACb,IAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChB,EAAA,CAzFID,MAAM;EAAA,QAEOH,WAAW;AAAA;AAAAgC,EAAA,GAFxB7B,MAAM;AA2FZ,eAAeA,MAAM;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}