{"ast": null, "code": "var _jsxFileName = \"E:\\\\boat\\\\src\\\\components\\\\Footer.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-1 md:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-ocean-400\",\n              children: \"\\uD83D\\uDEA4 Andaman Charters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 12,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300 mb-4 max-w-md\",\n            children: \"Experience the pristine waters of Andaman with our premium boat charter services. From private tours to game fishing adventures, we provide unforgettable maritime experiences.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"text-gray-300 hover:text-ocean-400 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Facebook\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 24,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"text-gray-300 hover:text-ocean-400 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Instagram\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987c6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297L3.323 17.49c.875.807 2.026 1.297 3.323 1.297h7.137c1.297 0 2.448-.49 3.323-1.297l-1.803-1.799c-.875.807-2.026 1.297-3.323 1.297H8.449z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"text-gray-300 hover:text-ocean-400 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"WhatsApp\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.864 3.488\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"text-gray-300 hover:text-ocean-400 transition-colors\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                className: \"text-gray-300 hover:text-ocean-400 transition-colors\",\n                children: \"About Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/services\",\n                className: \"text-gray-300 hover:text-ocean-400 transition-colors\",\n                children: \"Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/gallery\",\n                className: \"text-gray-300 hover:text-ocean-400 transition-colors\",\n                children: \"Gallery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/booking\",\n                className: \"text-gray-300 hover:text-ocean-400 transition-colors\",\n                children: \"Book Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Contact Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2 text-gray-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5 mr-2\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Port Blair, Andaman Islands\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5 mr-2\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"+91 98765 43210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5 mr-2\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-700 mt-8 pt-8 text-center text-gray-300\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2024 Andaman Charters. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "fill", "viewBox", "d", "to", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "_c", "$RefreshReg$"], "sources": ["E:/boat/src/components/Footer.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center mb-4\">\n              <span className=\"text-2xl font-bold text-ocean-400\">\n                🚤 Andaman Charters\n              </span>\n            </div>\n            <p className=\"text-gray-300 mb-4 max-w-md\">\n              Experience the pristine waters of Andaman with our premium boat charter services. \n              From private tours to game fishing adventures, we provide unforgettable maritime experiences.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-gray-300 hover:text-ocean-400 transition-colors\">\n                <span className=\"sr-only\">Facebook</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-300 hover:text-ocean-400 transition-colors\">\n                <span className=\"sr-only\">Instagram</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987c6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297L3.323 17.49c.875.807 2.026 1.297 3.323 1.297h7.137c1.297 0 2.448-.49 3.323-1.297l-1.803-1.799c-.875.807-2.026 1.297-3.323 1.297H8.449z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-300 hover:text-ocean-400 transition-colors\">\n                <span className=\"sr-only\">WhatsApp</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.864 3.488\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link to=\"/\" className=\"text-gray-300 hover:text-ocean-400 transition-colors\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link to=\"/about\" className=\"text-gray-300 hover:text-ocean-400 transition-colors\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link to=\"/services\" className=\"text-gray-300 hover:text-ocean-400 transition-colors\">\n                  Services\n                </Link>\n              </li>\n              <li>\n                <Link to=\"/gallery\" className=\"text-gray-300 hover:text-ocean-400 transition-colors\">\n                  Gallery\n                </Link>\n              </li>\n              <li>\n                <Link to=\"/booking\" className=\"text-gray-300 hover:text-ocean-400 transition-colors\">\n                  Book Now\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Contact Info</h3>\n            <div className=\"space-y-2 text-gray-300\">\n              <div className=\"flex items-center\">\n                <svg className=\"h-5 w-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n                <span>Port Blair, Andaman Islands</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg className=\"h-5 w-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <span>+91 98765 43210</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg className=\"h-5 w-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n                <span><EMAIL></span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center text-gray-300\">\n          <p>&copy; 2024 Andaman Charters. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACxCH,OAAA;MAAKE,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC3DH,OAAA;QAAKE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDH,OAAA;UAAKE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCH,OAAA;YAAKE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eACrCH,OAAA;cAAME,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNP,OAAA;YAAGE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAG3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJP,OAAA;YAAKE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BH,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBAC1EH,OAAA;gBAAME,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCP,OAAA;gBAAKE,SAAS,EAAC,SAAS;gBAACO,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAP,QAAA,eAC9DH,OAAA;kBAAMW,CAAC,EAAC;gBAAwQ;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/Q,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBAC1EH,OAAA;gBAAME,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1CP,OAAA;gBAAKE,SAAS,EAAC,SAAS;gBAACO,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAP,QAAA,eAC9DH,OAAA;kBAAMW,CAAC,EAAC;gBAAoU;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3U,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBAC1EH,OAAA;gBAAME,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCP,OAAA;gBAAKE,SAAS,EAAC,SAAS;gBAACO,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAP,QAAA,eAC9DH,OAAA;kBAAMW,CAAC,EAAC;gBAAklC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzlC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DP,OAAA;YAAIE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBH,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACc,EAAE,EAAC,GAAG;gBAACV,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAE9E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLP,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACc,EAAE,EAAC,QAAQ;gBAACV,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLP,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACc,EAAE,EAAC,WAAW;gBAACV,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAEtF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLP,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACc,EAAE,EAAC,UAAU;gBAACV,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAErF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLP,OAAA;cAAAG,QAAA,eACEH,OAAA,CAACF,IAAI;gBAACc,EAAE,EAAC,UAAU;gBAACV,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAErF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DP,OAAA;YAAKE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCH,OAAA;cAAKE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCH,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAACO,IAAI,EAAC,MAAM;gBAACI,MAAM,EAAC,cAAc;gBAACH,OAAO,EAAC,WAAW;gBAAAP,QAAA,gBACjFH,OAAA;kBAAMc,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACL,CAAC,EAAC;gBAAoF;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5JP,OAAA;kBAAMc,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACL,CAAC,EAAC;gBAAkC;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvG,CAAC,eACNP,OAAA;gBAAAG,QAAA,EAAM;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNP,OAAA;cAAKE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCH,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAACO,IAAI,EAAC,MAAM;gBAACI,MAAM,EAAC,cAAc;gBAACH,OAAO,EAAC,WAAW;gBAAAP,QAAA,eACjFH,OAAA;kBAAMc,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACL,CAAC,EAAC;gBAAuN;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5R,CAAC,eACNP,OAAA;gBAAAG,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACNP,OAAA;cAAKE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCH,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAACO,IAAI,EAAC,MAAM;gBAACI,MAAM,EAAC,cAAc;gBAACH,OAAO,EAAC,WAAW;gBAAAP,QAAA,eACjFH,OAAA;kBAAMc,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACL,CAAC,EAAC;gBAAsG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3K,CAAC,eACNP,OAAA;gBAAAG,QAAA,EAAM;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,8DAA8D;QAAAC,QAAA,eAC3EH,OAAA;UAAAG,QAAA,EAAG;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACU,EAAA,GAvGIhB,MAAM;AAyGZ,eAAeA,MAAM;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}