{"ast": null, "code": "var _jsxFileName = \"E:\\\\boat\\\\src\\\\pages\\\\Booking.jsx\";\nimport React from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport BookingForm from '../components/BookingForm';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Booking = () => {\n  const quickInfo = [{\n    icon: '📞',\n    title: 'Call Us',\n    description: '+91 98765 43210',\n    action: 'tel:+919876543210'\n  }, {\n    icon: '📧',\n    title: 'Email Us',\n    description: '<EMAIL>',\n    action: 'mailto:<EMAIL>'\n  }, {\n    icon: '💬',\n    title: 'WhatsApp',\n    description: 'Quick Response',\n    action: 'https://wa.me/919876543210'\n  }];\n  const bookingSteps = [{\n    step: '1',\n    title: 'Choose Your Service',\n    description: 'Select from our range of boat charter services'\n  }, {\n    step: '2',\n    title: 'Fill the Form',\n    description: 'Provide your details and preferences'\n  }, {\n    step: '3',\n    title: 'Get Confirmation',\n    description: 'We\\'ll contact you within 2 hours to confirm'\n  }, {\n    step: '4',\n    title: 'Make Payment',\n    description: 'Secure your booking with advance payment'\n  }];\n  const policies = [{\n    title: 'Booking Policy',\n    points: ['50% advance payment required to confirm booking', 'Balance payment can be made on the day of service', 'Booking confirmation will be sent via email/SMS', 'Valid ID proof required for all passengers']\n  }, {\n    title: 'Cancellation Policy',\n    points: ['Free cancellation up to 24 hours before departure', 'Cancellation within 24 hours: 50% refund', 'No refund for no-shows or same-day cancellations', 'Weather-related cancellations: Full refund or reschedule']\n  }, {\n    title: 'Safety Guidelines',\n    points: ['Life jackets mandatory for all passengers', 'Children under 12 must be accompanied by adults', 'No alcohol consumption during tours', 'Follow crew instructions at all times']\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Book Your Charter - Andaman Charters | Online Booking\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Book your boat charter in Andaman Islands online. Easy booking process for private tours, fishing charters, and island hopping adventures.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"keywords\",\n        content: \"book Andaman boat charter, online booking, boat tour reservation, fishing charter booking\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pt-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"relative py-20 bg-gradient-to-r from-ocean-600 to-ocean-800\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n            children: \"Book Your Charter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-ocean-100 max-w-3xl mx-auto\",\n            children: \"Reserve your spot for an unforgettable maritime adventure in the Andaman Islands\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"py-8 bg-gray-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-2\",\n              children: \"Need Immediate Assistance?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Contact us directly for instant booking support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n            children: quickInfo.map((info, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n              href: info.action,\n              className: \"bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl mb-3\",\n                children: info.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-1\",\n                children: info.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-ocean-600\",\n                children: info.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-bold text-gray-900 mb-4\",\n              children: \"How to Book\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-gray-600\",\n              children: \"Simple steps to secure your charter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n            children: bookingSteps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-ocean-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4\",\n                children: step.step\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                children: step.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: step.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-3 gap-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-2\",\n              children: /*#__PURE__*/_jsxDEV(BookingForm, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold text-gray-900 mb-4\",\n                  children: \"Popular Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border-l-4 border-ocean-500 pl-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-gray-900\",\n                      children: \"Half Day Island Tour\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 text-sm\",\n                      children: \"4 hours \\u2022 From \\u20B98,000\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border-l-4 border-ocean-500 pl-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-gray-900\",\n                      children: \"Game Fishing Charter\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 text-sm\",\n                      children: \"6 hours \\u2022 From \\u20B915,000\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 167,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border-l-4 border-ocean-500 pl-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-gray-900\",\n                      children: \"Sunset Cruise\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 text-sm\",\n                      children: \"2 hours \\u2022 From \\u20B95,000\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold text-gray-900 mb-4\",\n                  children: \"What to Bring\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-2 text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-green-500 mr-2\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 23\n                    }, this), \"Valid photo ID\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-green-500 mr-2\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 188,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 23\n                    }, this), \"Sunscreen and hat\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-green-500 mr-2\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 194,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 23\n                    }, this), \"Comfortable clothing\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-green-500 mr-2\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 23\n                    }, this), \"Camera/phone\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-green-500 mr-2\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 23\n                    }, this), \"Towel (for water activities)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-yellow-600 mr-3 mt-1\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-lg font-semibold text-yellow-800 mb-2\",\n                      children: \"Weather Dependent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-yellow-700 text-sm\",\n                      children: \"All boat services are subject to weather conditions. In case of rough seas or storms, we may need to reschedule or cancel for safety reasons. Full refund or rescheduling will be offered in such cases.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"py-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-bold text-gray-900 mb-4\",\n              children: \"Booking Policies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-gray-600\",\n              children: \"Please read our policies before booking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n            children: policies.map((policy, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                children: policy.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2\",\n                children: policy.points.map((point, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: [\"\\u2022 \", point]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = Booking;\nexport default Booking;\nvar _c;\n$RefreshReg$(_c, \"Booking\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "BookingForm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Booking", "quickInfo", "icon", "title", "description", "action", "bookingSteps", "step", "policies", "points", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "className", "map", "info", "index", "href", "fill", "viewBox", "fillRule", "d", "clipRule", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "policy", "point", "idx", "_c", "$RefreshReg$"], "sources": ["E:/boat/src/pages/Booking.jsx"], "sourcesContent": ["import React from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport BookingForm from '../components/BookingForm';\n\nconst Booking = () => {\n  const quickInfo = [\n    {\n      icon: '📞',\n      title: 'Call Us',\n      description: '+91 98765 43210',\n      action: 'tel:+919876543210'\n    },\n    {\n      icon: '📧',\n      title: 'Email Us',\n      description: '<EMAIL>',\n      action: 'mailto:<EMAIL>'\n    },\n    {\n      icon: '💬',\n      title: 'WhatsApp',\n      description: 'Quick Response',\n      action: 'https://wa.me/919876543210'\n    }\n  ];\n\n  const bookingSteps = [\n    {\n      step: '1',\n      title: 'Choose Your Service',\n      description: 'Select from our range of boat charter services'\n    },\n    {\n      step: '2',\n      title: 'Fill the Form',\n      description: 'Provide your details and preferences'\n    },\n    {\n      step: '3',\n      title: 'Get Confirmation',\n      description: 'We\\'ll contact you within 2 hours to confirm'\n    },\n    {\n      step: '4',\n      title: 'Make Payment',\n      description: 'Secure your booking with advance payment'\n    }\n  ];\n\n  const policies = [\n    {\n      title: 'Booking Policy',\n      points: [\n        '50% advance payment required to confirm booking',\n        'Balance payment can be made on the day of service',\n        'Booking confirmation will be sent via email/SMS',\n        'Valid ID proof required for all passengers'\n      ]\n    },\n    {\n      title: 'Cancellation Policy',\n      points: [\n        'Free cancellation up to 24 hours before departure',\n        'Cancellation within 24 hours: 50% refund',\n        'No refund for no-shows or same-day cancellations',\n        'Weather-related cancellations: Full refund or reschedule'\n      ]\n    },\n    {\n      title: 'Safety Guidelines',\n      points: [\n        'Life jackets mandatory for all passengers',\n        'Children under 12 must be accompanied by adults',\n        'No alcohol consumption during tours',\n        'Follow crew instructions at all times'\n      ]\n    }\n  ];\n\n  return (\n    <>\n      <Helmet>\n        <title>Book Your Charter - Andaman Charters | Online Booking</title>\n        <meta name=\"description\" content=\"Book your boat charter in Andaman Islands online. Easy booking process for private tours, fishing charters, and island hopping adventures.\" />\n        <meta name=\"keywords\" content=\"book Andaman boat charter, online booking, boat tour reservation, fishing charter booking\" />\n      </Helmet>\n\n      <div className=\"pt-16\">\n        {/* Hero Section */}\n        <section className=\"relative py-20 bg-gradient-to-r from-ocean-600 to-ocean-800\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n              Book Your Charter\n            </h1>\n            <p className=\"text-xl text-ocean-100 max-w-3xl mx-auto\">\n              Reserve your spot for an unforgettable maritime adventure in the Andaman Islands\n            </p>\n          </div>\n        </section>\n\n        {/* Quick Contact Options */}\n        <section className=\"py-8 bg-gray-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Need Immediate Assistance?</h2>\n              <p className=\"text-gray-600\">Contact us directly for instant booking support</p>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              {quickInfo.map((info, index) => (\n                <a\n                  key={index}\n                  href={info.action}\n                  className=\"bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow duration-200\"\n                >\n                  <div className=\"text-3xl mb-3\">{info.icon}</div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">{info.title}</h3>\n                  <p className=\"text-ocean-600\">{info.description}</p>\n                </a>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Booking Steps */}\n        <section className=\"py-12\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">How to Book</h2>\n              <p className=\"text-lg text-gray-600\">Simple steps to secure your charter</p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {bookingSteps.map((step, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"bg-ocean-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4\">\n                    {step.step}\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{step.title}</h3>\n                  <p className=\"text-gray-600\">{step.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Main Booking Section */}\n        <section className=\"py-16 bg-gray-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-12\">\n              {/* Booking Form */}\n              <div className=\"lg:col-span-2\">\n                <BookingForm />\n              </div>\n\n              {/* Sidebar Information */}\n              <div className=\"space-y-6\">\n                {/* Popular Services */}\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Popular Services</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"border-l-4 border-ocean-500 pl-4\">\n                      <h4 className=\"font-semibold text-gray-900\">Half Day Island Tour</h4>\n                      <p className=\"text-gray-600 text-sm\">4 hours • From ₹8,000</p>\n                    </div>\n                    <div className=\"border-l-4 border-ocean-500 pl-4\">\n                      <h4 className=\"font-semibold text-gray-900\">Game Fishing Charter</h4>\n                      <p className=\"text-gray-600 text-sm\">6 hours • From ₹15,000</p>\n                    </div>\n                    <div className=\"border-l-4 border-ocean-500 pl-4\">\n                      <h4 className=\"font-semibold text-gray-900\">Sunset Cruise</h4>\n                      <p className=\"text-gray-600 text-sm\">2 hours • From ₹5,000</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* What to Bring */}\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">What to Bring</h3>\n                  <ul className=\"space-y-2 text-gray-600\">\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Valid photo ID\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Sunscreen and hat\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Comfortable clothing\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Camera/phone\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Towel (for water activities)\n                    </li>\n                  </ul>\n                </div>\n\n                {/* Weather Notice */}\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n                  <div className=\"flex items-start\">\n                    <svg className=\"w-6 h-6 text-yellow-600 mr-3 mt-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z\" />\n                    </svg>\n                    <div>\n                      <h4 className=\"text-lg font-semibold text-yellow-800 mb-2\">Weather Dependent</h4>\n                      <p className=\"text-yellow-700 text-sm\">\n                        All boat services are subject to weather conditions. In case of rough seas or storms, \n                        we may need to reschedule or cancel for safety reasons. Full refund or rescheduling \n                        will be offered in such cases.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Policies Section */}\n        <section className=\"py-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Booking Policies</h2>\n              <p className=\"text-lg text-gray-600\">Please read our policies before booking</p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {policies.map((policy, index) => (\n                <div key={index} className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">{policy.title}</h3>\n                  <ul className=\"space-y-2\">\n                    {policy.points.map((point, idx) => (\n                      <li key={idx} className=\"text-gray-600 text-sm\">\n                        • {point}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  );\n};\n\nexport default Booking;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,MAAMC,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,iBAAiB;IAC9BC,MAAM,EAAE;EACV,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,0BAA0B;IACvCC,MAAM,EAAE;EACV,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,gBAAgB;IAC7BC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,GAAG;IACTJ,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACEG,IAAI,EAAE,GAAG;IACTJ,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEG,IAAI,EAAE,GAAG;IACTJ,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACEG,IAAI,EAAE,GAAG;IACTJ,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMI,QAAQ,GAAG,CACf;IACEL,KAAK,EAAE,gBAAgB;IACvBM,MAAM,EAAE,CACN,iDAAiD,EACjD,mDAAmD,EACnD,iDAAiD,EACjD,4CAA4C;EAEhD,CAAC,EACD;IACEN,KAAK,EAAE,qBAAqB;IAC5BM,MAAM,EAAE,CACN,mDAAmD,EACnD,0CAA0C,EAC1C,kDAAkD,EAClD,0DAA0D;EAE9D,CAAC,EACD;IACEN,KAAK,EAAE,mBAAmB;IAC1BM,MAAM,EAAE,CACN,2CAA2C,EAC3C,iDAAiD,EACjD,qCAAqC,EACrC,uCAAuC;EAE3C,CAAC,CACF;EAED,oBACEZ,OAAA,CAAAE,SAAA;IAAAW,QAAA,gBACEb,OAAA,CAACH,MAAM;MAAAgB,QAAA,gBACLb,OAAA;QAAAa,QAAA,EAAO;MAAqD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpEjB,OAAA;QAAMkB,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA4I;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChLjB,OAAA;QAAMkB,IAAI,EAAC,UAAU;QAACC,OAAO,EAAC;MAA2F;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtH,CAAC,eAETjB,OAAA;MAAKoB,SAAS,EAAC,OAAO;MAAAP,QAAA,gBAEpBb,OAAA;QAASoB,SAAS,EAAC,6DAA6D;QAAAP,QAAA,eAC9Eb,OAAA;UAAKoB,SAAS,EAAC,oDAAoD;UAAAP,QAAA,gBACjEb,OAAA;YAAIoB,SAAS,EAAC,gDAAgD;YAAAP,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLjB,OAAA;YAAGoB,SAAS,EAAC,0CAA0C;YAAAP,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVjB,OAAA;QAASoB,SAAS,EAAC,iBAAiB;QAAAP,QAAA,eAClCb,OAAA;UAAKoB,SAAS,EAAC,wCAAwC;UAAAP,QAAA,gBACrDb,OAAA;YAAKoB,SAAS,EAAC,kBAAkB;YAAAP,QAAA,gBAC/Bb,OAAA;cAAIoB,SAAS,EAAC,uCAAuC;cAAAP,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrFjB,OAAA;cAAGoB,SAAS,EAAC,eAAe;cAAAP,QAAA,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACNjB,OAAA;YAAKoB,SAAS,EAAC,uCAAuC;YAAAP,QAAA,EACnDT,SAAS,CAACiB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBvB,OAAA;cAEEwB,IAAI,EAAEF,IAAI,CAACd,MAAO;cAClBY,SAAS,EAAC,8FAA8F;cAAAP,QAAA,gBAExGb,OAAA;gBAAKoB,SAAS,EAAC,eAAe;gBAAAP,QAAA,EAAES,IAAI,CAACjB;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDjB,OAAA;gBAAIoB,SAAS,EAAC,0CAA0C;gBAAAP,QAAA,EAAES,IAAI,CAAChB;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1EjB,OAAA;gBAAGoB,SAAS,EAAC,gBAAgB;gBAAAP,QAAA,EAAES,IAAI,CAACf;cAAW;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAN/CM,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOT,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVjB,OAAA;QAASoB,SAAS,EAAC,OAAO;QAAAP,QAAA,eACxBb,OAAA;UAAKoB,SAAS,EAAC,wCAAwC;UAAAP,QAAA,gBACrDb,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAP,QAAA,gBAChCb,OAAA;cAAIoB,SAAS,EAAC,uCAAuC;cAAAP,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtEjB,OAAA;cAAGoB,SAAS,EAAC,uBAAuB;cAAAP,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAENjB,OAAA;YAAKoB,SAAS,EAAC,uCAAuC;YAAAP,QAAA,EACnDJ,YAAY,CAACY,GAAG,CAAC,CAACX,IAAI,EAAEa,KAAK,kBAC5BvB,OAAA;cAAiBoB,SAAS,EAAC,aAAa;cAAAP,QAAA,gBACtCb,OAAA;gBAAKoB,SAAS,EAAC,gHAAgH;gBAAAP,QAAA,EAC5HH,IAAI,CAACA;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACNjB,OAAA;gBAAIoB,SAAS,EAAC,0CAA0C;gBAAAP,QAAA,EAAEH,IAAI,CAACJ;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1EjB,OAAA;gBAAGoB,SAAS,EAAC,eAAe;gBAAAP,QAAA,EAAEH,IAAI,CAACH;cAAW;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAL3CM,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVjB,OAAA;QAASoB,SAAS,EAAC,kBAAkB;QAAAP,QAAA,eACnCb,OAAA;UAAKoB,SAAS,EAAC,wCAAwC;UAAAP,QAAA,eACrDb,OAAA;YAAKoB,SAAS,EAAC,wCAAwC;YAAAP,QAAA,gBAErDb,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAP,QAAA,eAC5Bb,OAAA,CAACF,WAAW;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAGNjB,OAAA;cAAKoB,SAAS,EAAC,WAAW;cAAAP,QAAA,gBAExBb,OAAA;gBAAKoB,SAAS,EAAC,mCAAmC;gBAAAP,QAAA,gBAChDb,OAAA;kBAAIoB,SAAS,EAAC,sCAAsC;kBAAAP,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1EjB,OAAA;kBAAKoB,SAAS,EAAC,WAAW;kBAAAP,QAAA,gBACxBb,OAAA;oBAAKoB,SAAS,EAAC,kCAAkC;oBAAAP,QAAA,gBAC/Cb,OAAA;sBAAIoB,SAAS,EAAC,6BAA6B;sBAAAP,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEjB,OAAA;sBAAGoB,SAAS,EAAC,uBAAuB;sBAAAP,QAAA,EAAC;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACNjB,OAAA;oBAAKoB,SAAS,EAAC,kCAAkC;oBAAAP,QAAA,gBAC/Cb,OAAA;sBAAIoB,SAAS,EAAC,6BAA6B;sBAAAP,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEjB,OAAA;sBAAGoB,SAAS,EAAC,uBAAuB;sBAAAP,QAAA,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACNjB,OAAA;oBAAKoB,SAAS,EAAC,kCAAkC;oBAAAP,QAAA,gBAC/Cb,OAAA;sBAAIoB,SAAS,EAAC,6BAA6B;sBAAAP,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9DjB,OAAA;sBAAGoB,SAAS,EAAC,uBAAuB;sBAAAP,QAAA,EAAC;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNjB,OAAA;gBAAKoB,SAAS,EAAC,mCAAmC;gBAAAP,QAAA,gBAChDb,OAAA;kBAAIoB,SAAS,EAAC,sCAAsC;kBAAAP,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvEjB,OAAA;kBAAIoB,SAAS,EAAC,yBAAyB;kBAAAP,QAAA,gBACrCb,OAAA;oBAAIoB,SAAS,EAAC,mBAAmB;oBAAAP,QAAA,gBAC/Bb,OAAA;sBAAKoB,SAAS,EAAC,6BAA6B;sBAACK,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAb,QAAA,eAClFb,OAAA;wBAAM2B,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,oHAAoH;wBAACC,QAAQ,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClK,CAAC,kBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLjB,OAAA;oBAAIoB,SAAS,EAAC,mBAAmB;oBAAAP,QAAA,gBAC/Bb,OAAA;sBAAKoB,SAAS,EAAC,6BAA6B;sBAACK,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAb,QAAA,eAClFb,OAAA;wBAAM2B,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,oHAAoH;wBAACC,QAAQ,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClK,CAAC,qBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLjB,OAAA;oBAAIoB,SAAS,EAAC,mBAAmB;oBAAAP,QAAA,gBAC/Bb,OAAA;sBAAKoB,SAAS,EAAC,6BAA6B;sBAACK,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAb,QAAA,eAClFb,OAAA;wBAAM2B,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,oHAAoH;wBAACC,QAAQ,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClK,CAAC,wBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLjB,OAAA;oBAAIoB,SAAS,EAAC,mBAAmB;oBAAAP,QAAA,gBAC/Bb,OAAA;sBAAKoB,SAAS,EAAC,6BAA6B;sBAACK,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAb,QAAA,eAClFb,OAAA;wBAAM2B,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,oHAAoH;wBAACC,QAAQ,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClK,CAAC,gBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLjB,OAAA;oBAAIoB,SAAS,EAAC,mBAAmB;oBAAAP,QAAA,gBAC/Bb,OAAA;sBAAKoB,SAAS,EAAC,6BAA6B;sBAACK,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAb,QAAA,eAClFb,OAAA;wBAAM2B,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,oHAAoH;wBAACC,QAAQ,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClK,CAAC,gCAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGNjB,OAAA;gBAAKoB,SAAS,EAAC,sDAAsD;gBAAAP,QAAA,eACnEb,OAAA;kBAAKoB,SAAS,EAAC,kBAAkB;kBAAAP,QAAA,gBAC/Bb,OAAA;oBAAKoB,SAAS,EAAC,mCAAmC;oBAACK,IAAI,EAAC,MAAM;oBAACK,MAAM,EAAC,cAAc;oBAACJ,OAAO,EAAC,WAAW;oBAAAb,QAAA,eACtGb,OAAA;sBAAM+B,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACL,CAAC,EAAC;oBAA2I;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChN,CAAC,eACNjB,OAAA;oBAAAa,QAAA,gBACEb,OAAA;sBAAIoB,SAAS,EAAC,4CAA4C;sBAAAP,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjFjB,OAAA;sBAAGoB,SAAS,EAAC,yBAAyB;sBAAAP,QAAA,EAAC;oBAIvC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVjB,OAAA;QAASoB,SAAS,EAAC,OAAO;QAAAP,QAAA,eACxBb,OAAA;UAAKoB,SAAS,EAAC,wCAAwC;UAAAP,QAAA,gBACrDb,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAP,QAAA,gBAChCb,OAAA;cAAIoB,SAAS,EAAC,uCAAuC;cAAAP,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EjB,OAAA;cAAGoB,SAAS,EAAC,uBAAuB;cAAAP,QAAA,EAAC;YAAuC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eAENjB,OAAA;YAAKoB,SAAS,EAAC,uCAAuC;YAAAP,QAAA,EACnDF,QAAQ,CAACU,GAAG,CAAC,CAACa,MAAM,EAAEX,KAAK,kBAC1BvB,OAAA;cAAiBoB,SAAS,EAAC,mCAAmC;cAAAP,QAAA,gBAC5Db,OAAA;gBAAIoB,SAAS,EAAC,0CAA0C;gBAAAP,QAAA,EAAEqB,MAAM,CAAC5B;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5EjB,OAAA;gBAAIoB,SAAS,EAAC,WAAW;gBAAAP,QAAA,EACtBqB,MAAM,CAACtB,MAAM,CAACS,GAAG,CAAC,CAACc,KAAK,EAAEC,GAAG,kBAC5BpC,OAAA;kBAAcoB,SAAS,EAAC,uBAAuB;kBAAAP,QAAA,GAAC,SAC5C,EAACsB,KAAK;gBAAA,GADDC,GAAG;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GARGM,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACoB,EAAA,GAhQIlC,OAAO;AAkQb,eAAeA,OAAO;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}