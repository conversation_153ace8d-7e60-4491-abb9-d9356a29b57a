import React from 'react';
import { Helmet } from 'react-helmet-async';

const AboutUs = () => {
  const fleet = [
    {
      name: 'Ocean Explorer',
      type: 'Luxury Yacht',
      capacity: '12 passengers',
      features: ['Air conditioning', 'Premium sound system', 'Fishing equipment', 'Safety gear'],
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'
    },
    {
      name: 'Island Hopper',
      type: 'Speed Boat',
      capacity: '8 passengers',
      features: ['High-speed cruising', 'Snorkeling gear', 'Cooler box', 'First aid kit'],
      image: 'https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'
    },
    {
      name: 'Fishing Master',
      type: 'Fishing Boat',
      capacity: '6 passengers',
      features: ['Professional fishing gear', 'Fish finder', 'Live bait tank', 'Cleaning station'],
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'
    }
  ];

  const team = [
    {
      name: 'Captain <PERSON>esh <PERSON>',
      role: 'Lead Captain',
      experience: '15 years',
      description: 'Expert navigator with extensive knowledge of Andaman waters and marine life.',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
    },
    {
      name: 'Captain Suresh Nair',
      role: 'Fishing Guide',
      experience: '12 years',
      description: 'Professional fishing guide specializing in deep-sea and game fishing.',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
    },
    {
      name: 'Maria D\'Souza',
      role: 'Tour Coordinator',
      experience: '8 years',
      description: 'Ensures seamless tour experiences and customer satisfaction.',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
    }
  ];

  return (
    <>
      <Helmet>
        <title>About Us - Andaman Charters | Our Story & Fleet</title>
        <meta name="description" content="Learn about Andaman Charters - our story, experienced team, and luxury fleet. Providing premium boat charter services in Andaman Islands since 2010." />
        <meta name="keywords" content="about Andaman Charters, boat charter company, Andaman boat fleet, experienced captains" />
      </Helmet>

      <div className="pt-16">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-r from-ocean-600 to-ocean-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              About Andaman Charters
            </h1>
            <p className="text-xl text-ocean-100 max-w-3xl mx-auto">
              Your trusted partner for unforgettable maritime adventures in the pristine waters of the Andaman Islands
            </p>
          </div>
        </section>

        {/* Our Story Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  Our Story
                </h2>
                <div className="space-y-4 text-gray-600">
                  <p>
                    Founded in 2010, Andaman Charters began as a small family business with a simple mission: 
                    to share the breathtaking beauty of the Andaman Islands with visitors from around the world. 
                    What started with a single boat and a passion for the sea has grown into one of the region's 
                    most trusted charter services.
                  </p>
                  <p>
                    Our founder, Captain Rajesh Kumar, grew up on these islands and has been navigating these 
                    waters for over two decades. His deep knowledge of the local marine environment, combined 
                    with a commitment to safety and excellence, forms the foundation of our company.
                  </p>
                  <p>
                    Today, we operate a modern fleet of well-maintained vessels and employ a team of experienced 
                    professionals who share our passion for providing exceptional maritime experiences. Every 
                    charter is designed to showcase the natural wonders of the Andaman Islands while ensuring 
                    the highest standards of safety and comfort.
                  </p>
                </div>
              </div>
              <div>
                <img
                  src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Andaman Islands sunset"
                  className="rounded-lg shadow-lg"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Mission & Values */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Our Mission & Values
              </h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="bg-ocean-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-ocean-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Safety First</h3>
                <p className="text-gray-600">
                  We prioritize the safety of our guests above all else, with regular maintenance, 
                  certified equipment, and experienced crew members.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-ocean-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-ocean-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Environmental Care</h3>
                <p className="text-gray-600">
                  We are committed to preserving the pristine marine environment of the Andaman Islands 
                  for future generations.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-ocean-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-ocean-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Excellence</h3>
                <p className="text-gray-600">
                  We strive for excellence in every aspect of our service, from our vessels 
                  to our customer experience.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Our Fleet */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Our Fleet
              </h2>
              <p className="text-xl text-gray-600">
                Modern, well-maintained vessels equipped for comfort and safety
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {fleet.map((boat, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
                  <img
                    src={boat.image}
                    alt={boat.name}
                    className="w-full h-48 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{boat.name}</h3>
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-ocean-600 font-medium">{boat.type}</span>
                      <span className="text-gray-600">{boat.capacity}</span>
                    </div>
                    <ul className="space-y-2">
                      {boat.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-gray-600">
                          <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Our Team */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Meet Our Team
              </h2>
              <p className="text-xl text-gray-600">
                Experienced professionals dedicated to your safety and enjoyment
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden text-center">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-32 h-32 rounded-full mx-auto mt-6 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-1">{member.name}</h3>
                    <p className="text-ocean-600 font-medium mb-2">{member.role}</p>
                    <p className="text-gray-500 mb-3">{member.experience} experience</p>
                    <p className="text-gray-600">{member.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Certifications */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
              Certifications & Safety
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="flex flex-col items-center">
                <div className="bg-ocean-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                  <span className="text-2xl">🛡️</span>
                </div>
                <h3 className="font-semibold text-gray-900">Licensed Operators</h3>
                <p className="text-gray-600 text-sm">All captains are licensed by maritime authorities</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="bg-ocean-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                  <span className="text-2xl">🚨</span>
                </div>
                <h3 className="font-semibold text-gray-900">Safety Equipment</h3>
                <p className="text-gray-600 text-sm">Latest safety gear and emergency equipment</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="bg-ocean-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                  <span className="text-2xl">📋</span>
                </div>
                <h3 className="font-semibold text-gray-900">Regular Inspections</h3>
                <p className="text-gray-600 text-sm">Vessels undergo regular safety inspections</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="bg-ocean-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                  <span className="text-2xl">🏥</span>
                </div>
                <h3 className="font-semibold text-gray-900">First Aid Certified</h3>
                <p className="text-gray-600 text-sm">Crew members trained in first aid and CPR</p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default AboutUs;
