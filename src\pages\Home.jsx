import React from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

const Home = () => {
  const features = [
    {
      icon: '🚤',
      title: 'Luxury Fleet',
      description: 'Modern, well-maintained boats with all safety equipment and amenities for your comfort.'
    },
    {
      icon: '🎣',
      title: 'Expert Guides',
      description: 'Experienced local captains and fishing guides who know the best spots and conditions.'
    },
    {
      icon: '🏝️',
      title: 'Island Hopping',
      description: 'Explore pristine beaches and hidden coves across the beautiful Andaman archipelago.'
    },
    {
      icon: '🌅',
      title: 'Sunset Cruises',
      description: 'Romantic sunset experiences with breathtaking views of the Indian Ocean.'
    }
  ];

  const testimonials = [
    {
      name: '<PERSON>',
      location: 'Mumbai, India',
      text: 'Absolutely incredible experience! The boat was luxurious and the crew was professional. The fishing was amazing and we caught some beautiful fish.',
      rating: 5
    },
    {
      name: '<PERSON>',
      location: 'Singapore',
      text: 'Perfect day out with family. The island hopping tour was well organized and the kids loved snorkeling in the crystal clear waters.',
      rating: 5
    },
    {
      name: '<PERSON><PERSON>',
      location: 'Delhi, India',
      text: 'The sunset cruise was magical! Great service, beautiful boat, and unforgettable memories. Highly recommended for couples.',
      rating: 5
    }
  ];

  const packages = [
    {
      title: 'Half Day Island Tour',
      duration: '4 hours',
      price: 'From ₹8,000',
      features: ['Visit 2-3 islands', 'Snorkeling gear included', 'Refreshments', 'Professional guide'],
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
      title: 'Game Fishing Charter',
      duration: '6 hours',
      price: 'From ₹15,000',
      features: ['Deep sea fishing', 'All equipment provided', 'Expert fishing guide', 'Lunch included'],
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
      title: 'Full Day Adventure',
      duration: '8 hours',
      price: 'From ₹20,000',
      features: ['Multiple islands', 'Water sports', 'Beach BBQ lunch', 'Photography'],
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Andaman Charters - Premium Boat Charter Services in Andaman Islands</title>
        <meta name="description" content="Experience the pristine waters of Andaman with our premium boat charter services. Private tours, game fishing, and island hopping adventures." />
        <meta name="keywords" content="Andaman boat charter, private boat tours, game fishing Andaman, island hopping, yacht rental Andaman" />
        <meta property="og:title" content="Andaman Charters - Premium Boat Charter Services" />
        <meta property="og:description" content="Experience the pristine waters of Andaman with our premium boat charter services." />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url(https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80)'
          }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        </div>
        
        <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Discover Andaman's
            <span className="block text-ocean-400">Hidden Paradise</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Experience luxury boat charters, game fishing, and island hopping in the pristine waters of the Andaman Islands
          </p>
          <div className="space-x-4">
            <Link
              to="/booking"
              className="inline-block bg-ocean-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-ocean-700 transition-colors duration-200"
            >
              Book Now
            </Link>
            <Link
              to="/services"
              className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-900 transition-colors duration-200"
            >
              Our Services
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Andaman Charters?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We provide exceptional maritime experiences with safety, comfort, and adventure at the forefront
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Popular Packages
            </h2>
            <p className="text-xl text-gray-600">
              Choose from our carefully crafted experiences
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <img
                  src={pkg.image}
                  alt={pkg.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{pkg.title}</h3>
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-gray-600">{pkg.duration}</span>
                    <span className="text-2xl font-bold text-ocean-600">{pkg.price}</span>
                  </div>
                  <ul className="space-y-2 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-gray-600">
                        <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Link
                    to="/booking"
                    className="w-full bg-ocean-600 text-white py-2 px-4 rounded-md hover:bg-ocean-700 transition-colors duration-200 text-center block"
                  >
                    Book This Package
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Our Guests Say
            </h2>
            <p className="text-xl text-gray-600">
              Real experiences from our satisfied customers
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <p className="text-gray-600 mb-4">"{testimonial.text}"</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-gray-500">{testimonial.location}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-ocean-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready for Your Adventure?
          </h2>
          <p className="text-xl text-ocean-100 mb-8 max-w-2xl mx-auto">
            Book your charter today and create memories that will last a lifetime in the beautiful Andaman Islands
          </p>
          <div className="space-x-4">
            <Link
              to="/booking"
              className="inline-block bg-white text-ocean-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              Book Your Charter
            </Link>
            <Link
              to="/contact"
              className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-ocean-600 transition-colors duration-200"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};

export default Home;
