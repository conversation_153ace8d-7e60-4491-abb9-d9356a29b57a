import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import Gallery from '../components/Gallery';

const GalleryPage = () => {
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Photos' },
    { id: 'boats', name: 'Our Fleet' },
    { id: 'tours', name: 'Tours & Cruises' },
    { id: 'fishing', name: 'Fishing Adventures' },
    { id: 'destinations', name: 'Destinations' },
    { id: 'activities', name: 'Activities' }
  ];

  const allImages = [
    {
      id: 1,
      src: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Luxury yacht Ocean Explorer in pristine Andaman waters',
      category: 'boats'
    },
    {
      id: 2,
      src: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Spectacular sunset cruise with golden hour lighting',
      category: 'tours'
    },
    {
      id: 3,
      src: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Deep sea game fishing - catching a magnificent tuna',
      category: 'fishing'
    },
    {
      id: 4,
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Ross Island - historical ruins and pristine beaches',
      category: 'destinations'
    },
    {
      id: 5,
      src: 'https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Speed boat Island Hopper ready for adventure',
      category: 'boats'
    },
    {
      id: 6,
      src: 'https://images.unsplash.com/photo-1530549387789-4c1017266635?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Snorkeling in crystal clear waters with colorful coral',
      category: 'activities'
    },
    {
      id: 7,
      src: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Professional fishing guide with fresh catch of the day',
      category: 'fishing'
    },
    {
      id: 8,
      src: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'North Bay Island - perfect for water sports',
      category: 'destinations'
    },
    {
      id: 9,
      src: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Romantic dinner cruise under the stars',
      category: 'tours'
    },
    {
      id: 10,
      src: 'https://images.unsplash.com/photo-1530549387789-4c1017266635?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Scuba diving expedition to explore underwater life',
      category: 'activities'
    },
    {
      id: 11,
      src: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Fishing Master boat equipped with latest gear',
      category: 'boats'
    },
    {
      id: 12,
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Viper Island - historical significance and natural beauty',
      category: 'destinations'
    },
    {
      id: 13,
      src: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Big game fishing - marlin jumping out of water',
      category: 'fishing'
    },
    {
      id: 14,
      src: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Island hopping tour visiting multiple pristine locations',
      category: 'tours'
    },
    {
      id: 15,
      src: 'https://images.unsplash.com/photo-1530549387789-4c1017266635?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Kayaking through mangrove forests',
      category: 'activities'
    },
    {
      id: 16,
      src: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Elephant Beach - famous for water sports and coral',
      category: 'destinations'
    }
  ];

  const filteredImages = activeCategory === 'all' 
    ? allImages 
    : allImages.filter(image => image.category === activeCategory);

  const stats = [
    { number: '500+', label: 'Happy Customers' },
    { number: '50+', label: 'Islands Explored' },
    { number: '1000+', label: 'Successful Trips' },
    { number: '5★', label: 'Average Rating' }
  ];

  return (
    <>
      <Helmet>
        <title>Gallery - Andaman Charters | Photos of Our Boats & Tours</title>
        <meta name="description" content="Browse our gallery of boat charters, fishing adventures, and island tours in Andaman. See our fleet and the beautiful destinations we visit." />
        <meta name="keywords" content="Andaman boat photos, charter gallery, fishing photos, island tour pictures, yacht photos" />
      </Helmet>

      <div className="pt-16">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-r from-ocean-600 to-ocean-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Gallery
            </h1>
            <p className="text-xl text-ocean-100 max-w-3xl mx-auto">
              Explore our collection of memorable moments and breathtaking destinations in the Andaman Islands
            </p>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-12 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              {stats.map((stat, index) => (
                <div key={index}>
                  <div className="text-3xl md:text-4xl font-bold text-ocean-600 mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Category Filter */}
        <section className="py-8 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Browse by Category</h2>
              <div className="flex flex-wrap justify-center gap-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                      activeCategory === category.id
                        ? 'bg-ocean-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-ocean-50 hover:text-ocean-600'
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Gallery Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <Gallery 
              images={filteredImages} 
              title={`${categories.find(cat => cat.id === activeCategory)?.name || 'All Photos'} (${filteredImages.length})`}
            />
          </div>
        </section>

        {/* Video Section */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Experience Videos
              </h2>
              <p className="text-lg text-gray-600">
                Watch our adventures in action
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="aspect-w-16 aspect-h-9 bg-gray-200 flex items-center justify-center">
                  <div className="text-center">
                    <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-gray-500">Island Hopping Adventure</p>
                    <p className="text-sm text-gray-400">Video coming soon</p>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Island Hopping Tour Highlights
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Experience the beauty of multiple islands in one amazing day trip
                  </p>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="aspect-w-16 aspect-h-9 bg-gray-200 flex items-center justify-center">
                  <div className="text-center">
                    <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-gray-500">Deep Sea Fishing</p>
                    <p className="text-sm text-gray-400">Video coming soon</p>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Game Fishing Adventures
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Watch the excitement of deep sea fishing in Andaman waters
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Customer Photos Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Share Your Experience
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Tag us on social media to feature your photos in our gallery
              </p>
              <div className="flex justify-center space-x-6">
                <a href="#" className="text-ocean-600 hover:text-ocean-700 transition-colors">
                  <span className="sr-only">Instagram</span>
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987c6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297L3.323 17.49c.875.807 2.026 1.297 3.323 1.297h7.137c1.297 0 2.448-.49 3.323-1.297l-1.803-1.799c-.875.807-2.026 1.297-3.323 1.297H8.449z"/>
                  </svg>
                </a>
                <a href="#" className="text-ocean-600 hover:text-ocean-700 transition-colors">
                  <span className="sr-only">Facebook</span>
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
                  </svg>
                </a>
              </div>
              <p className="text-gray-500 mt-4">
                Use hashtag #AndamanCharters to be featured
              </p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-ocean-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to Create Your Own Memories?
            </h2>
            <p className="text-xl text-ocean-100 mb-8 max-w-2xl mx-auto">
              Book your charter today and become part of our gallery of amazing experiences
            </p>
            <div className="space-x-4">
              <a
                href="/booking"
                className="inline-block bg-white text-ocean-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
              >
                Book Your Adventure
              </a>
              <a
                href="/contact"
                className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-ocean-600 transition-colors duration-200"
              >
                Contact Us
              </a>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default GalleryPage;
